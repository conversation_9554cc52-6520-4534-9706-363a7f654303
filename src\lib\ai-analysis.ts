import OpenAI from "openai";
import {
  MarketData,
  AIAnalysisResult,
  TradingSignal,
  TechnicalIndicators,
  RiskManagement,
  TrendAnalysis,
  RollingStrategy,
  OpenAIConfig,
} from "@/types/trading";
import { configService } from "./config-service";

export class AIAnalysisService {
  private openai: OpenAI | null = null;
  private static instance: AIAnalysisService;
  private currentConfig: OpenAIConfig | null = null;

  constructor() {
    this.initializeOpenAI();
  }

  private initializeOpenAI() {
    // 只在服务端初始化 OpenAI 客户端
    if (typeof window !== "undefined") {
      // 在客户端，不初始化 OpenAI 客户端
      return;
    }

    const config = configService.getConfig();

    if (config.apiKey) {
      this.openai = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL,
      });
      this.currentConfig = config;
    }
  }

  static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * 重新初始化 OpenAI 客户端（当配置更新时调用）
   */
  reinitialize(): void {
    this.initializeOpenAI();
  }

  /**
   * 检查是否已配置 API
   */
  isConfigured(): boolean {
    // 在客户端，直接检查配置服务中的配置
    if (typeof window !== "undefined") {
      const config = configService.getConfig();
      return !!(config.apiKey && config.apiKey.trim());
    }

    // 在服务端，检查 OpenAI 客户端是否已初始化
    return this.openai !== null && this.currentConfig !== null;
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): OpenAIConfig | null {
    return this.currentConfig;
  }

  /**
   * 生成AI分析系统提示词
   */
  private generateSystemPrompt(): string {
    return `你是一位专业的加密货币交易分析师，专精于滚仓策略、庄家行为分析和市场心理学。你必须基于数据提供具体可执行的交易建议，并深度分析庄家意图和持仓分布。

## 滚仓策略核心原则：
1. **复利增长，不是复利爆仓** - 严格控制风险，保护利润
2. **只在趋势确立时加仓** - 避免在震荡市中盲目操作
3. **每次加仓都是独立决策** - 不冲动，不梭哈
4. **严格回撤止损** - 盈利必须保护，绝不倒亏

## 庄家行为分析核心：
🎯 **庄家心理分析**：
- 庄家在不同阶段的心理状态和操作意图
- 通过价格行为和成交量识别庄家情绪
- 判断庄家是否处于恐慌、贪婪或理性状态

🎯 **庄家操盘手法识别**：
- 对敲交易（Wash Trading）：识别虚假成交量
- 价格托盘：在关键位置的人工支撑
- 假突破：故意制造技术信号误导散户
- 挤压操作：通过大单压制或拉升制造恐慌

🎯 **做局模式识别**：
- 吸筹阶段：低位横盘震荡，逐步收集筹码
- 拉升阶段：放量突破，制造FOMO情绪
- 出货阶段：高位震荡，逐步分发筹码
- 砸盘阶段：快速下跌，制造恐慌抛售

🎯 **庄家意图推测**：
- 基于资金流向判断庄家下一步动作
- 分析庄家的目标价位和时间周期
- 评估庄家的控盘程度和操作难度



## 交易铁律：
✅ 趋势确认后才加仓（突破回踩不破 + 成交量确认）
✅ 基础仓位5%，每突破关键位加3%
✅ 止损随盈利上移，锁死利润
✅ 3-5倍底仓+浮动加仓策略
✅ 盈利保护止损（赚到的钱绝不让市场抢回去）
✅ 阶梯式减仓（行情尾声逐步落袋为安）
✅ 识别庄家意图，顺势而为，避免与庄家对抗
✅ 关注大户动向，跟随聪明钱的方向

## 风险控制：
- 最大单次风险不超过账户的2%
- 总持仓不超过账户的20%
- 严格执行止损，绝不抱有侥幸心理
- 市场不会奖励贪心，但一定会惩罚贪婪
- 识别庄家陷阱，避免在关键位置被套
- 现货与期货的风险差异化管理

## 分析要求（必须全部完成）：
1. **多时间维度综合分析** - 结合月线、日线、小时线、30分钟线、1分钟线数据
2. **趋势识别** - 明确判断短期(1-3天)、中期(1-2周)、长期(1-3月)趋势方向和强度
3. **关键位识别** - 基于技术指标和价格行为确定精确的支撑阻力位
4. **市场结构分析** - 评估当前是突破、回调、震荡还是反转阶段
5. **成交量分析** - 结合成交量变化确认价格走势的有效性
6. **庄家行为深度分析** - 识别庄家操盘手法、心理状态和意图
7. **持仓分布分析** - 分析大户、散户、机构的持仓变化和行为模式
8. **现货期货差异分析** - 根据市场类型提供差异化建议
9. **具体交易建议** - 提供明确的入场价格、止损位、止盈位和仓位大小

## 决策逻辑（严格遵循）：
### BUY信号条件：
- 短期趋势向上 + 中期趋势非空头
- 价格突破关键阻力位且成交量放大
- RSI > 30且MACD金叉或即将金叉
- 当前价格接近支撑位，风险可控

### SELL信号条件：
- 短期趋势向下 + 中期趋势非多头
- 价格跌破关键支撑位且成交量放大
- RSI < 70且MACD死叉或即将死叉
- 当前价格接近阻力位，上涨空间有限

### HOLD信号条件：
- 趋势不明确或多空力量均衡
- 成交量萎缩，缺乏方向性
- 技术指标发出矛盾信号
- 风险收益比不佳（<1.5:1）

## 数值计算要求：
1. **entryPrice**: 必须基于当前价格和技术分析给出具体入场价格，不能为0
2. **stopLoss**: 必须基于支撑阻力位和风险控制原则计算，不能为0
3. **takeProfit**: 必须提供至少2个止盈位，基于阻力位和风险收益比
4. **riskReward**: 必须计算实际的风险收益比 = (止盈价-入场价)/(入场价-止损价)
5. **winRate**: 基于当前市场条件和信号强度估算胜率(30-90%)
6. **expectedReturn**: 基于风险收益比和胜率计算期望收益率

## 输出格式要求：
- 必须返回纯JSON格式，不要包含任何markdown代码块标记
- 不要使用代码块标记包围响应
- 直接返回有效的JSON对象
- 所有数值字段不能为0或空数组（除非确实是HOLD信号）

请始终以数据为准，保持客观理性，但必须提供具体可执行的建议。避免过度保守，在风险可控的前提下积极寻找交易机会。`;
  }

  /**
   * 生成用户分析请求提示词
   */
  private generateAnalysisPrompt(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): string {
    const { symbol, monthly, daily, hourly, thirtyMin, oneMin } = marketData;

    // 获取最新价格和变化
    const latestPrice = oneMin[oneMin.length - 1]?.close || 0;
    const dailyChange = daily[daily.length - 1]?.changePercent || 0;
    const hourlyChange = hourly[hourly.length - 1]?.changePercent || 0;

    // 计算波动率和成交量分析
    const volatility = this.calculateVolatility(daily);
    const volume24h = daily[daily.length - 1]?.volume || 0;
    const avgVolume = daily.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;
    const volumeRatio = volume24h / avgVolume;

    // 计算价格相对位置
    const priceVsBollingerUpper =
      ((latestPrice - indicators.bollinger.upper) /
        indicators.bollinger.upper) *
      100;
    const priceVsBollingerLower =
      ((latestPrice - indicators.bollinger.lower) /
        indicators.bollinger.lower) *
      100;
    const priceVsEMA20 =
      ((latestPrice - indicators.ema.ema20) / indicators.ema.ema20) * 100;
    const priceVsEMA50 =
      ((latestPrice - indicators.ema.ema50) / indicators.ema.ema50) * 100;

    // MACD信号分析
    const macdSignal =
      indicators.macd.macd > indicators.macd.signal ? "金叉" : "死叉";
    const macdStrength = Math.abs(
      indicators.macd.macd - indicators.macd.signal
    );

    return `请分析 ${symbol} 的交易机会，必须提供具体可执行的交易建议：

## 市场数据概览：
- 当前价格: $${latestPrice.toFixed(4)}
- 24小时变化: ${dailyChange.toFixed(2)}%
- 1小时变化: ${hourlyChange.toFixed(2)}%
- 24小时成交量: ${volume24h.toFixed(0)}
- 7日平均成交量: ${avgVolume.toFixed(0)}
- 成交量比率: ${volumeRatio.toFixed(2)}x (${
      volumeRatio > 1.2 ? "放量" : volumeRatio < 0.8 ? "缩量" : "正常"
    })
- 波动率: ${volatility.toFixed(2)}%

## 技术指标详细分析：
- RSI(14): ${indicators.rsi.toFixed(2)} (${
      indicators.rsi > 70 ? "超买" : indicators.rsi < 30 ? "超卖" : "中性"
    })
- MACD: ${indicators.macd.macd.toFixed(
      4
    )} (信号线: ${indicators.macd.signal.toFixed(
      4
    )}, ${macdSignal}, 强度: ${macdStrength.toFixed(4)})
- 布林带位置: 上轨 ${indicators.bollinger.upper.toFixed(
      4
    )}, 中轨 ${indicators.bollinger.middle.toFixed(
      4
    )}, 下轨 ${indicators.bollinger.lower.toFixed(4)}
- 价格相对布林带: 距上轨${priceVsBollingerUpper.toFixed(
      2
    )}%, 距下轨${priceVsBollingerLower.toFixed(2)}%
- EMA均线: EMA20=${indicators.ema.ema20.toFixed(
      4
    )}, EMA50=${indicators.ema.ema50.toFixed(
      4
    )}, EMA200=${indicators.ema.ema200.toFixed(4)}
- 价格相对均线: 距EMA20${priceVsEMA20.toFixed(
      2
    )}%, 距EMA50${priceVsEMA50.toFixed(2)}%
- 关键位: 支撑${indicators.support.toFixed(
      4
    )}, 阻力${indicators.resistance.toFixed(4)}

## 多时间维度趋势分析：
### 月线趋势 (36个月):
最近3个月变化: ${monthly
      .slice(-3)
      .map((m) => `${m.changePercent.toFixed(1)}%`)
      .join(", ")}
月线趋势: ${
      monthly.slice(-3).every((m) => m.changePercent > 0)
        ? "强势上涨"
        : monthly.slice(-3).every((m) => m.changePercent < 0)
        ? "持续下跌"
        : "震荡整理"
    }

### 日线趋势 (30天):
最近7天变化: ${daily
      .slice(-7)
      .map((d) => `${d.changePercent.toFixed(1)}%`)
      .join(", ")}
日线趋势: ${
      daily.slice(-7).filter((d) => d.changePercent > 0).length >= 5
        ? "多头主导"
        : daily.slice(-7).filter((d) => d.changePercent < 0).length >= 5
        ? "空头主导"
        : "震荡"
    }

### 小时线趋势 (最近24小时):
变化: ${hourly
      .slice(-24)
      .map((h) => `${h.changePercent.toFixed(1)}%`)
      .join(", ")}
小时线动量: ${hourly
      .slice(-6)
      .reduce((sum, h) => sum + h.changePercent, 0)
      .toFixed(2)}% (最近6小时累计)

### 30分钟线 (最近6小时):
变化: ${thirtyMin
      .slice(-12)
      .map((t) => `${t.changePercent.toFixed(1)}%`)
      .join(", ")}

### 1分钟线 (最近30分钟):
变化: ${oneMin
      .slice(-30)
      .map((o) => `${o.changePercent.toFixed(1)}%`)
      .join(", ")}

## 庄家行为分析要求：
请基于以上数据深度分析：
1. **操盘手法识别**：
   - 是否存在对敲交易（成交量与价格变化不匹配）
   - 关键位置是否有人工托盘或压盘
   - 是否出现假突破诱导散户
   - 异常放量的真实性分析

2. **庄家心理状态**：
   - 当前庄家是否处于恐慌、贪婪或理性状态
   - 庄家对市场的控制信心程度
   - 庄家可能的下一步操作意图

3. **做局模式判断**：
   - 当前处于吸筹、拉升、出货还是砸盘阶段
   - 操作周期和目标价位预测
   - 庄家的成本区间估算



## 现货期货差异分析：
请根据交易品种特点分析：
- 如果是现货：关注长期价值投资机会
- 如果是期货：关注杠杆风险和资金费率影响
- 套利机会和风险差异化管理建议

## 分析要求（必须完成）：
基于以上数据，你必须：

1. **明确判断趋势方向** - 不能全部是NEUTRAL，必须基于数据给出倾向性判断
2. **提供具体交易信号** - 如果数据支持，必须给出BUY或SELL信号，包含具体价格
3. **计算精确的风险管理参数** - 所有数值不能为0，必须基于技术分析计算
4. **评估真实的胜率和收益** - 基于当前市场条件给出合理估算
5. **识别关键的支撑阻力位** - 提供多个层次的关键位
6. **深度分析庄家行为** - 识别操盘手法、心理状态和意图
7. **现货期货差异化建议** - 根据市场类型提供针对性建议

## 决策参考：
- 当前价格 vs 支撑位距离: ${(
      ((latestPrice - indicators.support) / indicators.support) *
      100
    ).toFixed(2)}%
- 当前价格 vs 阻力位距离: ${(
      ((indicators.resistance - latestPrice) / latestPrice) *
      100
    ).toFixed(2)}%
- RSI是否超买超卖: ${
      indicators.rsi > 70
        ? "超买，考虑卖出"
        : indicators.rsi < 30
        ? "超卖，考虑买入"
        : "RSI中性区域"
    }
- MACD信号: ${macdSignal}，强度${macdStrength > 10 ? "强" : "弱"}
- 成交量确认: ${volumeRatio > 1.2 ? "有成交量支撑" : "成交量不足，需谨慎"}

请以纯JSON格式返回分析结果，不要使用markdown代码块标记，直接返回有效的JSON对象，包含以下结构：
{
  "trends": {"shortTerm": "BULLISH/BEARISH/NEUTRAL", "mediumTerm": "BULLISH/BEARISH/NEUTRAL", "longTerm": "BULLISH/BEARISH/NEUTRAL"},
  "signal": {"direction": "BUY/SELL/HOLD", "confidence": 0-100, "entryPrice": number, "stopLoss": number, "takeProfit": [number, number], "leverage": 1-3, "reasoning": "详细说明"},
  "risk": {"maxDrawdown": 2-5, "riskReward": 1.5-5.0, "winRate": 30-90, "expectedReturn": -10到50},
  "condition": {"momentum": "STRONG/WEAK/NEUTRAL"},
  "levels": {"support": [number, number], "resistance": [number, number]},
  "marketMaker": {
    "manipulationSignals": {"washTrading": 0-100, "priceSupport": 0-100, "volumeSpike": 0-100, "falseBreakout": 0-100},
    "psychologyAnalysis": {"fearGreedIndex": 0-100, "marketSentiment": "EXTREME_FEAR/FEAR/NEUTRAL/GREED/EXTREME_GREED", "retailBehavior": "PANIC_SELLING/FOMO_BUYING/RATIONAL/ACCUMULATING", "smartMoneyFlow": "INFLOW/OUTFLOW/NEUTRAL"},
    "manipulationPatterns": {"pattern": "PUMP_DUMP/ACCUMULATION/DISTRIBUTION/SQUEEZE/NONE", "confidence": 0-100, "stage": "EARLY/MIDDLE/LATE/COMPLETION", "timeframe": "预计持续时间", "description": "详细描述"},
    "intentions": {"primaryGoal": "ACCUMULATE/DISTRIBUTE/SQUEEZE_SHORTS/SQUEEZE_LONGS/MAINTAIN_RANGE", "targetPrice": number, "confidence": 0-100, "reasoning": "推理过程"}
  },

  "spotFutures": {
    "marketType": "SPOT/FUTURES",
    "basisSpread": number,
    "fundingRate": number,
    "openInterest": number,
    "leverageImpact": {"averageLeverage": number, "liquidationRisk": "LOW/MEDIUM/HIGH", "cascadeLiquidationPotential": 0-100},
    "arbitrageOpportunities": {"spotFuturesPremium": number, "crossExchangeSpread": number, "fundingArbitrage": number},
    "recommendations": {"preferredMarket": "SPOT/FUTURES/BOTH", "reasoning": "推理", "riskAdjustments": ["风险调整建议"]}
  },
  "insights": "详细的市场洞察和交易逻辑，包含庄家分析",
  "warnings": ["具体的风险提示，包含庄家陷阱"]
}

注意：除非市场完全无方向且风险极高，否则不要返回全部NEUTRAL和0值。要基于数据积极寻找交易机会。`;
  }

  /**
   * 计算价格波动率
   */
  private calculateVolatility(data: any[]): number {
    if (data.length < 2) return 0;

    const returns = data
      .slice(1)
      .map((item, index) => Math.log(item.close / data[index].close));

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) /
      returns.length;

    return Math.sqrt(variance) * Math.sqrt(365) * 100; // 年化波动率
  }

  /**
   * 清理AI响应，移除markdown代码块标记
   */
  private cleanAIResponse(response: string): string {
    // 移除markdown代码块标记
    let cleaned = response.trim();

    // 移除开头的```json或```
    cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, "");

    // 移除结尾的```
    cleaned = cleaned.replace(/\n?\s*```\s*$/i, "");

    // 移除其他可能的markdown标记
    cleaned = cleaned.replace(/^```\s*$/gm, "");

    // 移除可能的额外空白字符
    cleaned = cleaned.replace(/^\s+|\s+$/g, "");

    // 如果响应以{开头但不以}结尾，尝试找到最后一个}
    if (cleaned.startsWith("{") && !cleaned.endsWith("}")) {
      const lastBraceIndex = cleaned.lastIndexOf("}");
      if (lastBraceIndex > 0) {
        cleaned = cleaned.substring(0, lastBraceIndex + 1);
      }
    }

    return cleaned;
  }

  /**
   * 执行AI分析（使用传入的配置）
   */
  async analyzeMarketWithConfig(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    config: OpenAIConfig,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): Promise<AIAnalysisResult> {
    // 验证配置
    if (!config.apiKey || !config.apiKey.trim()) {
      throw new Error("AI 服务未配置，请前往设置页面配置 OpenAI API");
    }

    // 创建临时的OpenAI客户端
    const openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
    });

    try {
      const systemPrompt = this.generateSystemPrompt();
      const analysisPrompt = this.generateAnalysisPrompt(
        marketData,
        indicators
      );

      const completion = await openai.chat.completions.create({
        model: config.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: analysisPrompt },
        ],
        stream: false,
        temperature: 0.3,
        max_tokens: 3000,
        response_format: { type: "json_object" },
      });

      console.log(
        "AI分析请求prompt:",
        JSON.stringify(
          [
            { role: "system", content: systemPrompt },
            { role: "user", content: analysisPrompt },
          ],
          null,
          2
        )
      );

      console.log("AI分析请求response:", JSON.stringify(completion, null, 2));

      const aiResponse = completion.choices[0]?.message?.content;

      console.log("AI分析结果:", aiResponse);

      if (!aiResponse) {
        throw new Error("AI分析响应为空");
      }

      // 清理并解析AI响应
      const cleanedResponse = this.cleanAIResponse(aiResponse);
      console.log("清理后的AI响应:", cleanedResponse);

      let parsedResponse;
      try {
        parsedResponse = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.error("JSON解析失败:", parseError);
        console.error("原始响应:", aiResponse);
        console.error("清理后响应:", cleanedResponse);
        throw new Error(
          `AI响应格式错误，无法解析为JSON: ${
            parseError instanceof Error ? parseError.message : "未知错误"
          }`
        );
      }

      // 构建标准化的分析结果
      const result: AIAnalysisResult = {
        symbol: marketData.symbol,
        timestamp: Date.now(),
        marketTrend: {
          shortTerm: parsedResponse.trends?.shortTerm || "NEUTRAL",
          mediumTerm: parsedResponse.trends?.mediumTerm || "NEUTRAL",
          longTerm: parsedResponse.trends?.longTerm || "NEUTRAL",
        },
        technicalIndicators: indicators,
        tradingSignal: {
          direction: parsedResponse.signal?.direction || "HOLD",
          confidence: parsedResponse.signal?.confidence || 50,
          entryPrice:
            parsedResponse.signal?.entryPrice ||
            marketData.oneMin[marketData.oneMin.length - 1]?.close ||
            0,
          stopLoss: parsedResponse.signal?.stopLoss || 0,
          takeProfit: parsedResponse.signal?.takeProfit || [],
          positionSize: this.calculatePositionSize(
            riskTolerance,
            parsedResponse.signal?.confidence || 50
          ),
          leverage: parsedResponse.signal?.leverage || 1,
          reasoning:
            parsedResponse.signal?.reasoning || "基于技术分析的综合判断",
        },
        riskManagement: {
          maxDrawdown: parsedResponse.risk?.maxDrawdown || 5,
          riskReward: parsedResponse.risk?.riskReward || 2,
          winRate: parsedResponse.risk?.winRate || 60,
          expectedReturn: parsedResponse.risk?.expectedReturn || 0,
        },
        marketCondition: {
          volatility: this.classifyVolatility(
            this.calculateVolatility(marketData.daily)
          ),
          volume: this.classifyVolume(marketData.daily),
          momentum: parsedResponse.condition?.momentum || "NEUTRAL",
        },
        keyLevels: {
          support: parsedResponse.levels?.support || [indicators.support],
          resistance: parsedResponse.levels?.resistance || [
            indicators.resistance,
          ],
        },
        // 新增：庄家行为分析
        marketMakerAnalysis: {
          manipulationSignals: {
            washTrading:
              parsedResponse.marketMaker?.manipulationSignals?.washTrading || 0,
            priceSupport:
              parsedResponse.marketMaker?.manipulationSignals?.priceSupport ||
              0,
            volumeSpike:
              parsedResponse.marketMaker?.manipulationSignals?.volumeSpike || 0,
            falseBreakout:
              parsedResponse.marketMaker?.manipulationSignals?.falseBreakout ||
              0,
          },
          psychologyAnalysis: {
            fearGreedIndex:
              parsedResponse.marketMaker?.psychologyAnalysis?.fearGreedIndex ||
              50,
            marketSentiment:
              parsedResponse.marketMaker?.psychologyAnalysis?.marketSentiment ||
              "NEUTRAL",
            retailBehavior:
              parsedResponse.marketMaker?.psychologyAnalysis?.retailBehavior ||
              "RATIONAL",
            smartMoneyFlow:
              parsedResponse.marketMaker?.psychologyAnalysis?.smartMoneyFlow ||
              "NEUTRAL",
          },
          manipulationPatterns: {
            pattern:
              parsedResponse.marketMaker?.manipulationPatterns?.pattern ||
              "NONE",
            confidence:
              parsedResponse.marketMaker?.manipulationPatterns?.confidence || 0,
            stage:
              parsedResponse.marketMaker?.manipulationPatterns?.stage ||
              "EARLY",
            timeframe:
              parsedResponse.marketMaker?.manipulationPatterns?.timeframe ||
              "未知",
            description:
              parsedResponse.marketMaker?.manipulationPatterns?.description ||
              "暂无明显操盘模式",
          },
          intentions: {
            primaryGoal:
              parsedResponse.marketMaker?.intentions?.primaryGoal ||
              "MAINTAIN_RANGE",
            targetPrice:
              parsedResponse.marketMaker?.intentions?.targetPrice || 0,
            confidence: parsedResponse.marketMaker?.intentions?.confidence || 0,
            reasoning:
              parsedResponse.marketMaker?.intentions?.reasoning ||
              "数据不足，无法判断庄家意图",
          },
        },

        // 新增：现货期货分析
        spotFuturesAnalysis: {
          marketType: "SPOT", // 默认现货，实际应根据symbol判断
          basisSpread: parsedResponse.spotFutures?.basisSpread,
          fundingRate: parsedResponse.spotFutures?.fundingRate,
          openInterest: parsedResponse.spotFutures?.openInterest,
          leverageImpact: parsedResponse.spotFutures?.leverageImpact
            ? {
                averageLeverage:
                  parsedResponse.spotFutures.leverageImpact.averageLeverage ||
                  1,
                liquidationRisk:
                  parsedResponse.spotFutures.leverageImpact.liquidationRisk ||
                  "LOW",
                cascadeLiquidationPotential:
                  parsedResponse.spotFutures.leverageImpact
                    .cascadeLiquidationPotential || 0,
              }
            : undefined,
          arbitrageOpportunities: parsedResponse.spotFutures
            ?.arbitrageOpportunities
            ? {
                spotFuturesPremium:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .spotFuturesPremium || 0,
                crossExchangeSpread:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .crossExchangeSpread || 0,
                fundingArbitrage:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .fundingArbitrage || 0,
              }
            : undefined,
          recommendations: {
            preferredMarket:
              parsedResponse.spotFutures?.recommendations?.preferredMarket ||
              "SPOT",
            reasoning:
              parsedResponse.spotFutures?.recommendations?.reasoning ||
              "基于当前市场条件的建议",
            riskAdjustments:
              parsedResponse.spotFutures?.recommendations?.riskAdjustments ||
              [],
          },
        },
        aiInsights:
          parsedResponse.insights ||
          "基于当前市场数据的综合分析，包含庄家行为分析",
        warnings: parsedResponse.warnings || [],
      };

      return result;
    } catch (error) {
      console.error("AI分析失败:", error);
      throw new Error("AI分析服务暂时不可用，请稍后重试");
    }
  }

  /**
   * 执行AI分析（使用实例配置，向后兼容）
   */
  async analyzeMarket(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): Promise<AIAnalysisResult> {
    if (!this.openai || !this.currentConfig) {
      throw new Error("AI 服务未配置，请前往设置页面配置 OpenAI API");
    }

    return this.analyzeMarketWithConfig(
      marketData,
      indicators,
      this.currentConfig,
      riskTolerance
    );
  }

  /**
   * 计算建议仓位大小
   */
  private calculatePositionSize(
    riskTolerance: string,
    confidence: number
  ): number {
    const baseSize =
      {
        LOW: 3,
        MEDIUM: 5,
        HIGH: 8,
      }[riskTolerance] || 5;

    // 根据信心度调整仓位
    const confidenceMultiplier = confidence / 100;
    return Math.min(baseSize * confidenceMultiplier, 10); // 最大10%
  }

  /**
   * 分类波动率
   */
  private classifyVolatility(volatility: number): "HIGH" | "MEDIUM" | "LOW" {
    if (volatility > 80) return "HIGH";
    if (volatility > 40) return "MEDIUM";
    return "LOW";
  }

  /**
   * 分类成交量
   */
  private classifyVolume(dailyData: any[]): "HIGH" | "MEDIUM" | "LOW" {
    if (dailyData.length < 7) return "MEDIUM";

    const recent = dailyData[dailyData.length - 1]?.volume || 0;
    const average =
      dailyData.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;

    const ratio = recent / average;
    if (ratio > 1.5) return "HIGH";
    if (ratio > 0.8) return "MEDIUM";
    return "LOW";
  }

  /**
   * 生成滚仓策略建议
   */
  async generateRollingStrategy(
    marketData: MarketData,
    analysisResult: AIAnalysisResult
  ): Promise<RollingStrategy> {
    const { tradingSignal, marketCondition } = analysisResult;

    // 基础仓位根据市场条件调整
    let basePosition = 5; // 默认5%
    if (marketCondition.volatility === "HIGH") basePosition = 3;
    if (marketCondition.volatility === "LOW") basePosition = 7;

    // 加仓阈值根据趋势强度调整
    const additionThreshold = tradingSignal.confidence > 80 ? 2 : 3;

    return {
      basePosition,
      additionThreshold,
      maxPosition: basePosition * 3, // 最大仓位为基础仓位的3倍
      profitTarget:
        tradingSignal.takeProfit[0] || tradingSignal.entryPrice * 1.05,
      stopLoss: tradingSignal.stopLoss,
      trendConfirmation: tradingSignal.confidence > 70,
    };
  }
}

export const aiAnalysisService = AIAnalysisService.getInstance();
