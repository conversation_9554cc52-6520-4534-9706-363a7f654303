// 交易相关的类型定义

export interface KlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
}

export interface ProcessedKlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface MarketData {
  symbol: string;
  monthly: ProcessedKlineData[]; // 36个月数据
  daily: ProcessedKlineData[]; // 30天数据
  hourly: ProcessedKlineData[]; // 168小时数据（7天）
  thirtyMin: ProcessedKlineData[]; // 48个30分钟数据（1天）
  oneMin: ProcessedKlineData[]; // 60个1分钟数据（1小时）
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema: {
    ema20: number;
    ema50: number;
    ema200: number;
  };
  support: number;
  resistance: number;
}

export interface TradingSignal {
  direction: "LONG" | "SHORT" | "HOLD";
  confidence: number; // 0-100
  entryPrice: number;
  stopLoss: number;
  takeProfit: number[];
  positionSize: number; // 建议仓位大小（百分比）
  leverage: number; // 建议杠杆倍数
  reasoning: string;
}

export interface RiskManagement {
  maxDrawdown: number;
  riskReward: number;
  winRate: number;
  expectedReturn: number;
}

export interface AIAnalysisResult {
  symbol: string;
  timestamp: number;
  marketTrend: {
    shortTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    mediumTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    longTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
  };
  technicalIndicators: TechnicalIndicators;
  tradingSignal: TradingSignal;
  riskManagement: RiskManagement;
  marketCondition: {
    volatility: "HIGH" | "MEDIUM" | "LOW";
    volume: "HIGH" | "MEDIUM" | "LOW";
    momentum: "STRONG" | "WEAK" | "NEUTRAL";
  };
  keyLevels: {
    support: number[];
    resistance: number[];
  };
  // 新增：庄家行为分析
  marketMakerAnalysis: MarketMakerAnalysis;
  // 新增：现货期货分析
  spotFuturesAnalysis: SpotFuturesAnalysis;
  aiInsights: string;
  warnings: string[];
}

export interface TradingAdvice {
  action: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  quantity: number;
  stopLoss: number;
  takeProfit: number[];
  timeframe: string;
  confidence: number;
  reasoning: string;
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
}

export interface PositionManagement {
  initialPosition: number;
  addPositions: {
    price: number;
    size: number;
    condition: string;
  }[];
  exitStrategy: {
    partialExits: {
      price: number;
      percentage: number;
    }[];
    stopLoss: number;
    trailingStop: boolean;
  };
}

export interface MarketAnalysisRequest {
  symbol: string;
  timeframes: ("1M" | "1d" | "1h" | "30m" | "1m")[];
  includeIndicators: boolean;
  riskTolerance: "LOW" | "MEDIUM" | "HIGH";
  openaiConfig: OpenAIConfig; // 添加OpenAI配置
}

// OpenAI API 配置相关类型
export interface OpenAIConfig {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigFormData {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigTestResult {
  success: boolean;
  message: string;
  latency?: number;
}

// 交易API配置相关类型
export interface TradingAPIConfig {
  baseURL: string;
  apiKey: string;
  secretKey: string;
  testnet?: boolean;
  name?: string; // 配置名称，如"主网"、"测试网"等
}

export interface TradingConfigFormData {
  baseURL: string;
  apiKey: string;
  secretKey: string;
  testnet: boolean;
  name: string;
}

export interface TradingConfigTestResult {
  success: boolean;
  message: string;
  latency?: number;
  accountInfo?: any; // 测试成功时返回的账户信息
}

export interface CoinInfo {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  search?: string;
  sortBy?: "symbol" | "price" | "change24h" | "volume24h";
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

// 滚仓策略相关类型
export interface RollingStrategy {
  basePosition: number; // 基础仓位百分比
  additionThreshold: number; // 加仓阈值
  maxPosition: number; // 最大仓位
  profitTarget: number; // 盈利目标
  stopLoss: number; // 止损点
  trendConfirmation: boolean; // 趋势确认
}

export interface TrendAnalysis {
  direction: "UP" | "DOWN" | "SIDEWAYS";
  strength: number; // 1-10
  duration: number; // 持续时间（小时）
  breakoutPotential: number; // 突破潜力 0-100
  volumeConfirmation: boolean;
}

// 庄家行为分析相关类型
export interface MarketMakerAnalysis {
  manipulationSignals: {
    washTrading: number; // 对敲交易可能性 0-100
    priceSupport: number; // 价格托盘强度 0-100
    volumeSpike: number; // 异常放量程度 0-100
    falseBreakout: number; // 假突破可能性 0-100
  };
  psychologyAnalysis: {
    fearGreedIndex: number; // 恐惧贪婪指数 0-100
    marketSentiment:
      | "EXTREME_FEAR"
      | "FEAR"
      | "NEUTRAL"
      | "GREED"
      | "EXTREME_GREED";
    retailBehavior:
      | "PANIC_SELLING"
      | "FOMO_BUYING"
      | "RATIONAL"
      | "ACCUMULATING";
    smartMoneyFlow: "INFLOW" | "OUTFLOW" | "NEUTRAL";
  };
  manipulationPatterns: {
    pattern: "PUMP_DUMP" | "ACCUMULATION" | "DISTRIBUTION" | "SQUEEZE" | "NONE";
    confidence: number; // 0-100
    stage: "EARLY" | "MIDDLE" | "LATE" | "COMPLETION";
    timeframe: string; // 预计持续时间
    description: string;
  };
  intentions: {
    primaryGoal:
      | "ACCUMULATE"
      | "DISTRIBUTE"
      | "SQUEEZE_SHORTS"
      | "SQUEEZE_LONGS"
      | "MAINTAIN_RANGE";
    targetPrice: number; // 庄家目标价位
    confidence: number; // 判断信心度 0-100
    reasoning: string;
  };
}

// 现货与期货差异分析
export interface SpotFuturesAnalysis {
  marketType: "SPOT" | "FUTURES";
  basisSpread?: number; // 基差（仅期货）
  fundingRate?: number; // 资金费率（仅期货）
  openInterest?: number; // 持仓量（仅期货）
  leverageImpact?: {
    averageLeverage: number;
    liquidationRisk: "LOW" | "MEDIUM" | "HIGH";
    cascadeLiquidationPotential: number; // 0-100
  };
  arbitrageOpportunities?: {
    spotFuturesPremium: number;
    crossExchangeSpread: number;
    fundingArbitrage: number;
  };
  recommendations: {
    preferredMarket: "SPOT" | "FUTURES" | "BOTH";
    reasoning: string;
    riskAdjustments: string[];
  };
}
